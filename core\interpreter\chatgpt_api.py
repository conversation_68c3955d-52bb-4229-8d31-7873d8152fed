"""OpenAI ChatGPT-4o integration helpers with advanced rate limiting and batching.

This module provides intelligent document processing with OpenAI's API, featuring:
- Request queue system with rate limiting
- Intelligent batching for multiple documents
- Enhanced retry logic with exponential backoff
- Proper handling of 429 (Too Many Requests) errors

Environment variables
---------------------
OPENAI_API_KEY
    The secret API key used for authenticating with the OpenAI service.

Any networking or JSON related error is retried with exponential back-off up to
``MAX_RETRIES`` times. If all attempts fail the function returns a minimal
fallback dict so that the caller can continue processing without crashing.
"""

from __future__ import annotations

from dotenv import load_dotenv
load_dotenv()

from typing import Dict, Any, List, Optional, Tuple
import os
import json
import time
import logging
import requests
import threading
from queue import Queue, Empty
from dataclasses import dataclass
from datetime import datetime, timedelta

__all__ = ["analyze_mail_and_pdf", "analyze_mail_and_batch_pdfs", "BatchRequestManager"]

log = logging.getLogger(__name__)

OPENAI_API_URL = "https://api.openai.com/v1/chat/completions"
MODEL = "gpt-4o"
TIMEOUT = 60  # Increased timeout for batch requests
MAX_RETRIES = 5  # Increased retries for rate limiting
BACKOFF_SECS = 2

# Rate limiting configuration
REQUESTS_PER_MINUTE = 50  # Conservative limit for GPT-4o
MIN_REQUEST_INTERVAL = 60.0 / REQUESTS_PER_MINUTE  # Minimum seconds between requests
RATE_LIMIT_BACKOFF = 60  # Seconds to wait on 429 error


@dataclass
class BatchRequest:
    """Represents a single request in the batch processing queue."""
    request_id: str
    mail_body: str
    document_texts: List[Dict[str, str]]  # List of {filename, text} dicts
    language: str
    is_batch: bool
    timestamp: datetime
    result: Optional[Dict[str, Any]] = None
    error: Optional[str] = None
    completed: bool = False


class BatchRequestManager:
    """Manages OpenAI API requests with intelligent batching and rate limiting."""

    def __init__(self):
        self._request_queue = Queue()
        self._results = {}
        self._last_request_time = 0
        self._lock = threading.Lock()
        self._processing = False

    def add_request(self, request_id: str, mail_body: str, document_texts: List[Dict[str, str]],
                   language: str = "English", is_batch: bool = False) -> str:
        """Add a request to the processing queue."""
        request = BatchRequest(
            request_id=request_id,
            mail_body=mail_body,
            document_texts=document_texts,
            language=language,
            is_batch=is_batch,
            timestamp=datetime.now()
        )
        self._request_queue.put(request)
        return request_id

    def get_result(self, request_id: str, timeout: float = 300) -> Optional[Dict[str, Any]]:
        """Get the result for a specific request, waiting up to timeout seconds."""
        start_time = time.time()
        while time.time() - start_time < timeout:
            with self._lock:
                if request_id in self._results:
                    result = self._results.pop(request_id)
                    return result
            time.sleep(0.1)
        return None

    def process_queue(self) -> None:
        """Process all requests in the queue with proper rate limiting."""
        if self._processing:
            return

        self._processing = True
        try:
            while not self._request_queue.empty():
                try:
                    request = self._request_queue.get_nowait()
                    self._process_single_request(request)
                except Empty:
                    break
        finally:
            self._processing = False

    def _process_single_request(self, request: BatchRequest) -> None:
        """Process a single request with rate limiting."""
        # Ensure minimum interval between requests
        current_time = time.time()
        time_since_last = current_time - self._last_request_time
        if time_since_last < MIN_REQUEST_INTERVAL:
            sleep_time = MIN_REQUEST_INTERVAL - time_since_last
            log.info(f"Rate limiting: sleeping {sleep_time:.2f}s before next request")
            time.sleep(sleep_time)

        try:
            if request.is_batch and len(request.document_texts) > 1:
                result = self._process_batch_request(request)
            else:
                # Single document processing
                doc_text = request.document_texts[0]['text'] if request.document_texts else ""
                result = self._process_single_document(request.mail_body, doc_text, request.language)

            with self._lock:
                self._results[request.request_id] = result

        except Exception as e:
            log.error(f"Failed to process request {request.request_id}: {e}")
            with self._lock:
                self._results[request.request_id] = {
                    "doc_type": "Unknown",
                    "summary": f"Processing failed: {str(e)}",
                    "extracted_fields": {},
                }
        finally:
            self._last_request_time = time.time()

    def _process_batch_request(self, request: BatchRequest) -> Dict[str, Any]:
        """Process a batch request with multiple documents."""
        # Combine all document texts into a single prompt
        combined_text = ""
        for i, doc in enumerate(request.document_texts, 1):
            filename = doc.get('filename', f'Document {i}')
            text = doc.get('text', '')
            combined_text += f"\n\n=== DOCUMENT {i}: {filename} ===\n{text}"

        messages = _build_messages(request.mail_body, combined_text, request.language, is_batch=True)

        try:
            raw = _post_chat(messages, is_batch=True)
            content = raw["choices"][0]["message"]["content"].strip()
            log.debug("RAW ChatGPT batch reply: %s", content[:500])
            result: Dict[str, Any] = json.loads(content)
            return result
        except Exception as exc:
            log.error("ChatGPT batch analysis failed – falling back to defaults: %s", exc)
            return {
                "doc_type": "Unknown",
                "summary": "ChatGPT batch analysis failed.",
                "extracted_fields": {},
            }

    def _process_single_document(self, mail_body: str, pdf_text: str, language: str) -> Dict[str, Any]:
        """Process a single document."""
        messages = _build_messages(mail_body, pdf_text, language, is_batch=False)

        try:
            raw = _post_chat(messages, is_batch=False)
            content = raw["choices"][0]["message"]["content"].strip()
            log.debug("RAW ChatGPT reply: %s", content[:500])
            result: Dict[str, Any] = json.loads(content)
            return result
        except Exception as exc:
            log.error("ChatGPT analysis failed – falling back to defaults: %s", exc)
            return {
                "doc_type": "Unknown",
                "summary": "ChatGPT analysis failed.",
                "extracted_fields": {},
            }


# Global batch manager instance
_batch_manager = BatchRequestManager()


_SYSTEM_PROMPT = (
    "You are an intelligent document processing assistant integrated into an "
    "email automation pipeline. You receive the TEXT BODY of an e-mail and "
    "the extracted TEXT from a PDF attachment. Analyse BOTH together to: "
    "(1) identify the high-level document type (e.g. invoice, certificate of "
    "analysis, offer, safety data sheet, purchase order, etc.), "
    "(2) extract any key/value data that would be useful for downstream systems, "
    "and (3) output a structured, detailed summary that provides clear insights. "
    "\n\nEMAIL BODY INTERPRETATION:\n"
    "- Pay special attention to instructions, requests, or specific guidance provided in the email body\n"
    "- If the sender mentions specific links, actions, or requests in the email, include these in the summary\n"
    "- ALWAYS capture contact information: phone numbers, extensions, email addresses, emergency contacts\n"
    "- Include any deadlines, time constraints, or urgency indicators\n"
    "- Incorporate any context or background information from the email that helps understand the document's purpose\n"
    "- Note any special handling instructions, approval workflows, or required actions mentioned in the email\n\n"
    "IMPORTANT EXTRACTION RULES:\n"
    "- When extracting batch numbers, lot numbers, or serial numbers:\n"
    "  * ONLY extract the actual alphanumeric identifier (e.g. '6098403', 'ABC123', 'LOT-456')\n"
    "  * NEVER extract dates as batch/lot/serial numbers\n"
    "  * If you see 'Batch: 2018-11-08', the batch number is NOT '2018-11-08' - look for the actual batch identifier\n"
    "  * Batch numbers are typically: numeric codes, alphanumeric codes, or product codes\n"
    "  * Manufacturing dates are separate from batch numbers\n"
    "- Dates should be extracted separately as date fields (e.g. 'manufacturing_date', "
    "'expiry_date', 'test_date', 'signing_date', 'document_date', etc.).\n"
    "- CRITICAL: Distinguish between batch identifiers and manufacturing dates:\n"
    "  * 'Batch Number: 6098403' → batch_number: '6098403'\n"
    "  * 'Tillverkningsdatum: 2018-11-08' → manufacturing_date: '2018-11-08'\n"
    "  * 'Manufacturing Date: 2018-11-08' → manufacturing_date: '2018-11-08'\n"
    "  * These are completely different fields - do not confuse them!\n"
    "- CRITICAL COMPANY NAME EXTRACTION (search in priority order):\n"
    "  * For Certificates/CoA: Extract manufacturer/supplier company name\n"
    "  * For Invoices: Extract vendor/seller company name (who issued the invoice)\n"
    "  * For Order Confirmations: Extract vendor/seller company name using this priority:\n"
    "    1. Document headers and letterheads (most reliable)\n"
    "    2. Email sender domain (e.g., '<EMAIL>' → 'Mio')\n"
    "    3. Company logos or brand names in document\n"
    "    4. Footer contact information\n"
    "    5. Website URLs (e.g., 'www.ikea.com' → 'IKEA')\n"
    "    6. Store names or branch information\n"
    "  * For Purchase Orders: Extract vendor/supplier company name (who will fulfill the order)\n"
    "  * For any commercial document: Extract the company providing the service/product\n"
    "  * COMPANY NAME FORMATTING: Use recognizable brand name only:\n"
    "    - Remove legal suffixes: AB, Inc., Ltd., LLC, Corp., GmbH, etc.\n"
    "    - Examples: 'TechShop AB' → 'TechShop', 'Mio E-handel AB' → 'Mio'\n"
    "    - Keep only the core brand/business name that customers recognize\n"
    "  * MANDATORY: company_name field must NEVER be empty, null, or 'Unknown'\n"
    "  * MANDATORY: Always include 'company_name' field in extracted_fields - never omit this field\n"
    "  * If company name appears in email addresses, store names, or contact info, extract it\n"
    "- Be precise with field names - use descriptive names like 'batch_number', "
    "'lot_number', 'invoice_number', 'po_number', etc.\n"
    "- If a field contains both letters and numbers, include the complete identifier.\n"
    "- ALWAYS extract 'document_year' as a 4-digit year (e.g. 2018, 2020) representing "
    "the most relevant year for this document. This could be:\n"
    "  * Manufacturing year (Tillverkningsdatum) for products/chemicals\n"
    "  * Test/analysis year for certificates and reports\n"
    "  * Invoice/document creation year for commercial documents\n"
    "  * The year when the document's subject matter was created/produced\n"
    "  * Use your best judgment to determine the most contextually relevant year\n\n"
    "INTELLIGENT DOCUMENT ANALYSIS:\n"
    "- Automatically determine if the document type requires parameter/specification analysis\n"
    "- Documents that typically need parameter analysis include:\n"
    "  * Certificate of Analysis (CoA) - test results vs specifications\n"
    "  * Quality Control Reports - measurements vs acceptable ranges\n"
    "  * Test Reports - performance metrics vs standards\n"
    "  * Inspection Reports - findings vs compliance requirements\n"
    "  * Safety Data Sheets - hazard levels vs safety thresholds\n"
    "  * Environmental Reports - emissions/levels vs regulatory limits\n"
    "  * Calibration Certificates - accuracy vs tolerance requirements\n"
    "  * Material Certificates - properties vs material standards\n"
    "- For documents requiring parameter analysis:\n"
    "  * SMART PARAMETER REPORTING: Only report parameters that are:\n"
    "    - Out of specification (failed)\n"
    "    - Close to specification limits (within 10% of limit)\n"
    "    - Specifically mentioned in email instructions\n"
    "    - Critical safety parameters\n"
    "  * Do NOT list all parameters if they are well within specification\n"
    "  * Summarize compliant parameters as 'All other parameters within specification'\n"
    "  * Identify specification limits, target values, acceptable ranges, or compliance criteria\n"
    "  * Compare actual results/values against these specifications\n"
    "  * Flag any parameters that are out of specification, borderline, or concerning\n"
    "  * Assess overall compliance status\n"
    "- Provide intelligent actionable guidance based on analysis:\n"
    "  * If all parameters meet specifications: 'Please review the attachment if you wish (optional review)'\n"
    "  * If minor deviations or borderline results: 'Please review the attachment - attention recommended for [specific areas]'\n"
    "  * If significant issues or non-compliance: 'Please review the attachment - attention required for [specific parameters]'\n"
    "  * If critical failures or safety concerns: 'URGENT: Please review the attachment immediately - critical issues identified'\n"
    "- For other document types, focus on the most critical information relevant to that document type\n\n"
    "SUMMARY FORMATTING RULES:\n"
    "- Create structured, detailed summaries using CLEAR SECTIONS with separators\n"
    "- Use this exact section structure for better readability:\n"
    "\n"
    "--- DOCUMENT INFO ---\n"
    "[Document type, key identifiers, dates, etc.]\n"
    "\n"
    "--- KEY FINDINGS ---\n"
    "[Critical results, compliance status, important parameters only]\n"
    "\n"
    "--- EMAIL INSTRUCTIONS ---\n"
    "[Instructions from email, contact info, deadlines, specific links mentioned]\n"
    "\n"
    "--- ACTION REQUIRED ---\n"
    "[Clear action items and recommendations]\n"
    "\n"
    "- IMPORTANT FORMATTING RULES:\n"
    "  * Use bullet points (•) within each section for multiple items\n"
    "  * Include actual URLs/links mentioned in emails, not generic 'provided link' text\n"
    "  * For technical documents: Only report concerning parameters, summarize others as 'All other parameters within specification'\n"
    "  * ALWAYS include contact information from emails: phone numbers, extensions, emergency contacts, email addresses\n"
    "  * Avoid redundant phrases - do not repeat 'Please review the attachment' multiple times\n"
    "  * Prioritize the most critical information that requires recipient attention\n\n"
    "Return your answer STRICTLY as a JSON object with these top-level keys: doc_type, "
    "summary, extracted_fields. The 'summary' must be a STRING containing the formatted text with sections. "
    "``extracted_fields`` must itself be a JSON object, or an empty object if not applicable. "
    "DO NOT wrap the JSON in markdown or any prose – output ONLY valid minified JSON."
)

_BATCH_SYSTEM_PROMPT = (
    "You are an intelligent document processing assistant integrated into an "
    "email automation pipeline. You receive the TEXT BODY of an e-mail and "
    "the extracted TEXT from MULTIPLE PDF attachments of the SAME document type. "
    "Analyse ALL documents together to: "
    "(1) identify the high-level document type (e.g. invoice, certificate of "
    "analysis, offer, safety data sheet, purchase order, etc.), "
    "(2) extract any key/value data that would be useful for downstream systems, "
    "and (3) output a structured, COMPACT summary optimized for multiple documents. "
    "\n\nBATCH PROCESSING RULES:\n"
    "- You are processing MULTIPLE documents of the same type from ONE email\n"
    "- Create a VERY COMPACT summary that lists all documents and highlights ONLY concerns/issues\n"
    "- Focus ONLY on CONCERNS, ISSUES, or CRITICAL INFORMATION across all documents\n"
    "- Do NOT repeat routine information that is the same across documents\n"
    "- For compliant/normal documents, mention them briefly in one line\n"
    "- STOP after EMAIL INSTRUCTIONS section - do NOT repeat document details\n\n"
    "EMAIL BODY INTERPRETATION:\n"
    "- Pay special attention to instructions, requests, or specific guidance provided in the email body\n"
    "- If the sender mentions specific links, actions, or requests in the email, include these in the summary\n"
    "- ALWAYS capture contact information: phone numbers, extensions, email addresses, emergency contacts\n"
    "- Include any deadlines, time constraints, or urgency indicators\n"
    "- Incorporate any context or background information from the email that helps understand the document's purpose\n"
    "- Note any special handling instructions, approval workflows, or required actions mentioned in the email\n\n"
    "IMPORTANT EXTRACTION RULES:\n"
    "- When extracting batch numbers, lot numbers, or serial numbers:\n"
    "  * ONLY extract the actual alphanumeric identifier (e.g. '6098403', 'ABC123', 'LOT-456')\n"
    "  * NEVER extract dates as batch/lot/serial numbers\n"
    "  * If you see 'Batch: 2018-11-08', the batch number is NOT '2018-11-08' - look for the actual batch identifier\n"
    "  * Batch numbers are typically: numeric codes, alphanumeric codes, or product codes\n"
    "  * Manufacturing dates are separate from batch numbers\n"
    "- Dates should be extracted separately as date fields (e.g. 'manufacturing_date', "
    "'expiry_date', 'test_date', 'signing_date', 'document_date', etc.).\n"
    "- CRITICAL: Distinguish between batch identifiers and manufacturing dates:\n"
    "  * 'Batch Number: 6098403' → batch_number: '6098403'\n"
    "  * 'Tillverkningsdatum: 2018-11-08' → manufacturing_date: '2018-11-08'\n"
    "  * 'Manufacturing Date: 2018-11-08' → manufacturing_date: '2018-11-08'\n"
    "  * These are completely different fields - do not confuse them!\n"
    "- ALWAYS extract company names as 'company_name' based on document type:\n"
    "  * For Certificates/CoA: Extract manufacturer/supplier company name\n"
    "  * For Invoices: Extract vendor/seller company name (who issued the invoice)\n"
    "  * For Order Confirmations: Extract vendor/seller company name using this priority:\n"
    "    1. Document headers and letterheads (most reliable)\n"
    "    2. Email sender domain (e.g., '<EMAIL>' → 'Mio')\n"
    "    3. Company logos or brand names in document\n"
    "    4. Footer contact information\n"
    "    5. Website URLs (e.g., 'www.ikea.com' → 'IKEA')\n"
    "    6. Store names or branch information\n"
    "  * For Purchase Orders: Extract vendor/supplier company name (who will fulfill the order)\n"
    "  * For any commercial document: Extract the company providing the service/product\n"
    "  * COMPANY NAME FORMATTING: Use recognizable brand name only:\n"
    "    - Remove legal suffixes: AB, Inc., Ltd., LLC, Corp., GmbH, etc.\n"
    "    - Examples: 'TechShop AB' → 'TechShop', 'Mio E-handel AB' → 'Mio'\n"
    "    - Keep only the core brand/business name that customers recognize\n"
    "  * MANDATORY: Always include 'company_name' field in extracted_fields - never omit this field\n"
    "  * MANDATORY: company_name field must NEVER be empty, null, or 'Unknown'\n"
    "- Be precise with field names - use descriptive names like 'batch_number', "
    "'lot_number', 'invoice_number', 'po_number', etc.\n"
    "- If a field contains both letters and numbers, include the complete identifier.\n"
    "- ALWAYS extract 'document_year' as a 4-digit year (e.g. 2018, 2020) representing "
    "the most relevant year for this document. This could be:\n"
    "  * Manufacturing year (Tillverkningsdatum) for products/chemicals\n"
    "  * Test/analysis year for certificates and reports\n"
    "  * Invoice/document creation year for commercial documents\n"
    "  * The year when the document's subject matter was created/produced\n"
    "  * Use your best judgment to determine the most contextually relevant year\n"
    "- Extract company names, contact information, addresses, phone numbers\n"
    "- Extract document identifiers, reference numbers, order numbers\n"
    "- Extract monetary amounts, quantities, units of measure\n"
    "- Extract any compliance, certification, or regulatory information\n"
    "- For technical documents: extract test results, specifications, parameters\n"
    "- Extract any dates (manufacturing, expiry, test, document, etc.)\n"
    "- Extract product information, descriptions, part numbers\n"
    "- Extract any quality control, inspection, or approval information\n\n"
    "INTELLIGENT BATCH DOCUMENT ANALYSIS:\n"
    "- Automatically determine if the document type requires parameter/specification analysis\n"
    "- Documents that typically need parameter analysis include:\n"
    "  * Certificate of Analysis (CoA) - test results vs specifications\n"
    "  * Quality Control Reports - measurements vs acceptable ranges\n"
    "  * Test Reports - performance metrics vs standards\n"
    "  * Inspection Reports - findings vs compliance requirements\n"
    "  * Safety Data Sheets - hazard levels vs safety thresholds\n"
    "  * Environmental Reports - emissions/levels vs regulatory limits\n"
    "  * Calibration Certificates - accuracy vs tolerance requirements\n"
    "  * Material Certificates - properties vs material standards\n"
    "- For documents requiring parameter analysis:\n"
    "  * SMART PARAMETER REPORTING: Only report parameters that are:\n"
    "    - Out of specification (failed)\n"
    "    - Close to specification limits (within 10% of limit)\n"
    "    - Specifically mentioned in email instructions\n"
    "    - Critical safety parameters\n"
    "  * Do NOT list all parameters if they are well within specification\n"
    "  * Summarize compliant parameters as 'All other parameters within specification'\n"
    "  * Identify specification limits, target values, acceptable ranges, or compliance criteria\n"
    "  * Compare actual results/values against these specifications\n"
    "  * Flag any parameters that are out of specification, borderline, or concerning\n"
    "  * Assess overall compliance status across all documents\n"
    "- For other document types, focus on the most critical information relevant to that document type\n\n"
    "BATCH SUMMARY FORMATTING RULES:\n"
    "- Create structured, COMPACT summaries using CLEAR SECTIONS with separators\n"
    "- Use this exact section structure for better readability:\n"
    "\n"
    "--- DOCUMENT INFO ---\n"
    "1. [First document filename]: [Brief description]\n"
    "2. [Second document filename]: [Brief description]\n"
    "[Continue for all documents]\n"
    "\n"
    "--- KEY FINDINGS ---\n"
    "• In [document name]: [Only if important/concerning findings]\n"
    "• In [document name]: [Only if important/concerning findings]\n"
    "• [Summary of compliant documents]: All other documents within specification\n"
    "\n"
    "--- EMAIL INSTRUCTIONS ---\n"
    "[Instructions from email, contact info, deadlines, specific links mentioned]\n"
    "\n"
    "CRITICAL: STOP HERE - Do NOT add any content after EMAIL INSTRUCTIONS section.\n"
    "\n"
    "- IMPORTANT BATCH FORMATTING RULES:\n"
    "  * List ALL documents in the DOCUMENT INFO section with brief descriptions\n"
    "  * In KEY FINDINGS, ONLY mention documents with concerns, issues, or important findings\n"
    "  * Summarize compliant documents as a group (e.g., 'Documents 1, 3, 5: All parameters within specification')\n"
    "  * Use bullet points (•) within each section for multiple items\n"
    "  * Include actual URLs/links mentioned in emails, not generic 'provided link' text\n"
    "  * ALWAYS include contact information from emails: phone numbers, extensions, emergency contacts, email addresses\n"
    "  * MANDATORY: END the summary immediately after EMAIL INSTRUCTIONS section\n"
    "  * Do NOT add ACTION REQUIRED, CONCLUSION, or any other sections\n"
    "  * Do NOT repeat document information after EMAIL INSTRUCTIONS\n"
    "  * Avoid redundant phrases - be concise and focus on what requires attention\n\n"
    "Return your answer STRICTLY as a JSON object with these top-level keys: doc_type, "
    "summary, extracted_fields. The 'summary' must be a STRING containing the formatted text with sections. "
    "``extracted_fields`` must itself be a JSON object, or an empty object if not applicable. "
    "DO NOT wrap the JSON in markdown or any prose – output ONLY valid minified JSON."
)


def _build_messages(mail_body: str, pdf_text: str, language: str = "English", is_batch: bool = False) -> List[Dict[str, str]]:
    """Compose the messages list for the Chat Completions endpoint."""

    user_prompt = (
        "EMAIL BODY:\n" + mail_body.strip() + "\n\n" +
        "PDF TEXT:\n" + pdf_text.strip() + "\n"
    )

    # Choose the appropriate system prompt based on batch mode
    system_prompt = _BATCH_SYSTEM_PROMPT if is_batch else _SYSTEM_PROMPT

    # Add language instruction to system prompt if not English
    if language.lower() != "english":
        system_prompt += f"\n\nIMPORTANT: Generate the summary in {language} language, but keep field names in English for system compatibility."

    return [
        {"role": "system", "content": system_prompt},
        {"role": "user", "content": user_prompt},
    ]


def _post_chat(messages: List[Dict[str, str]], is_batch: bool = False) -> Dict[str, Any]:
    """Low-level HTTP POST helper with enhanced rate limiting and retry logic."""
    api_key = os.getenv("OPENAI_API_KEY")
    if not api_key:
        raise RuntimeError("Environment variable OPENAI_API_KEY is not set.")

    headers = {
        "Authorization": f"Bearer {api_key}",
        "Content-Type": "application/json",
    }

    payload = {
        "model": MODEL,
        "messages": messages,
        "temperature": 0.0,
        "response_format": {"type": "json_object"},
    }

    # Adjust timeout for batch requests
    timeout = TIMEOUT * 2 if is_batch else TIMEOUT

    for attempt in range(1, MAX_RETRIES + 1):
        try:
            resp = requests.post(
                OPENAI_API_URL,
                headers=headers,
                json=payload,
                timeout=timeout,
            )

            # Handle rate limiting specifically
            if resp.status_code == 429:
                retry_after = int(resp.headers.get('retry-after', RATE_LIMIT_BACKOFF))
                log.warning(f"Rate limited (429). Waiting {retry_after}s before retry (attempt {attempt}/{MAX_RETRIES})")
                time.sleep(retry_after)
                continue

            resp.raise_for_status()
            data = resp.json()
            return data  # type: ignore[return-value]

        except requests.exceptions.Timeout:
            wait_time = BACKOFF_SECS ** attempt
            log.warning(f"Request timeout (attempt {attempt}/{MAX_RETRIES}). Waiting {wait_time}s")
            if attempt == MAX_RETRIES:
                raise
            time.sleep(wait_time)

        except (requests.RequestException, ValueError) as exc:
            wait_time = BACKOFF_SECS ** attempt
            log.warning(f"OpenAI request failed (attempt {attempt}/{MAX_RETRIES}): {exc}. Waiting {wait_time}s")
            if attempt == MAX_RETRIES:
                raise
            time.sleep(wait_time)
    # Unreachable – loop either returns or raises.


def analyze_mail_and_pdf(mail_body: str, pdf_text: str, language: str = "English", is_batch: bool = False, use_batch_manager: bool = True) -> Dict[str, Any]:
    """Send *mail_body* and *pdf_text* to ChatGPT-4o and return its JSON dict.

    If the request fails the function logs the error and returns a fallback
    result so that the caller can keep running without the ML component.

    Args:
        mail_body: The email body text
        pdf_text: The extracted PDF text
        language: The preferred language for the summary (default: English)
        is_batch: Whether this is batch processing of multiple documents (default: False)
        use_batch_manager: Whether to use the batch manager for rate limiting (default: True)
    """
    if use_batch_manager:
        # Use batch manager for rate limiting
        request_id = f"single_{int(time.time() * 1000)}"
        document_texts = [{"filename": "document.pdf", "text": pdf_text}]

        _batch_manager.add_request(request_id, mail_body, document_texts, language, is_batch)
        _batch_manager.process_queue()

        result = _batch_manager.get_result(request_id, timeout=120)
        if result:
            return result
        else:
            log.error("Batch manager timeout - falling back to direct processing")

    # Fallback to direct processing
    messages = _build_messages(mail_body, pdf_text, language, is_batch)

    try:
        raw = _post_chat(messages, is_batch)
        content = raw["choices"][0]["message"]["content"].strip()
        # Debug: log first 500 chars of the model's reply to diagnose JSON issues
        log.debug("RAW ChatGPT reply: %s", content[:500])
        # The model has been instructed to output JSON only.  Parse it.
        result: Dict[str, Any] = json.loads(content)
        return result
    except Exception as exc:  # broad catch so the pipeline never aborts here
        log.error("ChatGPT analysis failed – falling back to defaults: %s", exc)
        return {
            "doc_type": "Unknown",
            "summary": "ChatGPT analysis failed.",
            "extracted_fields": {},
        }


def analyze_mail_and_batch_pdfs(mail_body: str, document_texts: List[Dict[str, str]], language: str = "English", use_batch_manager: bool = True) -> Dict[str, Any]:
    """Send *mail_body* and multiple *document_texts* to ChatGPT-4o for batch analysis.

    Args:
        mail_body: The email body text
        document_texts: List of dicts with 'filename' and 'text' keys
        language: The preferred language for the summary (default: English)
        use_batch_manager: Whether to use the batch manager for rate limiting (default: True)

    Returns:
        Dict with doc_type, summary, and extracted_fields
    """
    if use_batch_manager:
        # Use batch manager for rate limiting
        request_id = f"batch_{int(time.time() * 1000)}"

        _batch_manager.add_request(request_id, mail_body, document_texts, language, is_batch=True)
        _batch_manager.process_queue()

        result = _batch_manager.get_result(request_id, timeout=180)  # Longer timeout for batch
        if result:
            return result
        else:
            log.error("Batch manager timeout - falling back to direct processing")

    # Fallback to direct processing
    # Combine all document texts into a single prompt
    combined_text = ""
    for i, doc in enumerate(document_texts, 1):
        filename = doc.get('filename', f'Document {i}')
        text = doc.get('text', '')
        combined_text += f"\n\n=== DOCUMENT {i}: {filename} ===\n{text}"

    # Use batch processing mode
    messages = _build_messages(mail_body, combined_text, language, is_batch=True)

    try:
        raw = _post_chat(messages, is_batch=True)
        content = raw["choices"][0]["message"]["content"].strip()
        # Debug: log first 500 chars of the model's reply to diagnose JSON issues
        log.debug("RAW ChatGPT batch reply: %s", content[:500])
        # The model has been instructed to output JSON only.  Parse it.
        result: Dict[str, Any] = json.loads(content)
        return result
    except Exception as exc:  # broad catch so the pipeline never aborts here
        log.error("ChatGPT batch analysis failed – falling back to defaults: %s", exc)
        return {
            "doc_type": "Unknown",
            "summary": "ChatGPT batch analysis failed.",
            "extracted_fields": {},
        }